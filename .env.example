# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/trustay_db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-here-make-it-long-and-complex-at-least-32-characters"
JWT_EXPIRES_IN="1h"

# Email Configuration (Resend)
RESEND_API_KEY="re_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Application Configuration
NODE_ENV="development"
PORT=3000

# Registration Configuration  
ALLOW_DIRECT_REGISTRATION=true  # Set to false in production to enforce verification

# SMS Configuration (Optional - for future implementation)
# TWILIO_ACCOUNT_SID="your_twilio_account_sid"
# TWILIO_AUTH_TOKEN="your_twilio_auth_token"
# TWILIO_PHONE_NUMBER="+**********"

# AWS SNS (Alternative SMS provider)
# AWS_REGION="us-east-1"
# AWS_ACCESS_KEY_ID="your_aws_access_key"
# AWS_SECRET_ACCESS_KEY="your_aws_secret_key"

# CORS Configuration
CORS_ORIGIN="http://localhost:3000,https://trustay.life"

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# File Upload Configuration
MAX_FILE_SIZE=5242880 # 5MB in bytes
UPLOAD_DEST="./uploads"

# Logging Configuration
LOG_LEVEL="info"