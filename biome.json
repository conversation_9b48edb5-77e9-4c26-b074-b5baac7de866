{"$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "vcs": {"enabled": false, "clientKind": "git"}, "files": {"ignoreUnknown": true, "includes": ["src/**/*.ts", "test/**/*.ts", "**/*.json", "**/*.js", "!**/*.min.ts", "!**/*.min.js", "!dist/**/*", "!node_modules/**/*", "!coverage/**/*", "!*.config.js", "!*.config.mjs", "!jest.config.js", "!webpack.config.js", "!logs/**/*"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "tab", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "auto"}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "useImportType": "off", "useConst": "warn", "useTemplate": "warn", "useNumberNamespace": "off"}, "suspicious": {"noThenProperty": "off", "noExplicitAny": "off", "noArrayIndexKey": "warn", "noAssignInExpressions": "warn", "noAsyncPromiseExecutor": "warn", "noEmptyBlockStatements": "off"}, "complexity": {"noExcessiveCognitiveComplexity": "warn", "noForEach": "off", "useLiteralKeys": "off", "useOptionalChain": "warn"}, "correctness": {"noUnusedVariables": "warn", "noUnusedImports": "warn", "useExhaustiveDependencies": "off", "useHookAtTopLevel": "off"}, "security": {"noDangerouslySetInnerHtml": "off"}, "performance": {"noDelete": "warn"}, "nursery": {"useSortedClasses": "off"}}}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false}, "parser": {"unsafeParameterDecoratorsEnabled": true}, "globals": ["NodeJS", "jest", "describe", "it", "expect", "beforeEach", "after<PERSON>ach", "beforeAll", "afterAll"]}, "json": {"formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}}, "css": {"formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}