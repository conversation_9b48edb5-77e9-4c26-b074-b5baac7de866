services:
  trustay-postgres:
    image: postgres:17-alpine
    restart: unless-stopped
    container_name: trustay-postgres
    env_file:
      - .env
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_INITDB_ARGS=${POSTGRES_INITDB_ARGS}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "1206:5432"
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - trustay-network

  trustay-redis:
    image: redis:7-alpine
    restart: unless-stopped
    container_name: trustay-redis
    env_file:
      - .env
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 128M
    networks:
      - trustay-network


  trustay-pgweb:
    image: sosedoff/pgweb
    restart: unless-stopped
    container_name: trustay-pgweb
    environment:
      - DATABASE_URL=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@trustay-postgres:5432/${POSTGRES_DB}?sslmode=disable

    ports:
      - "8081:8081"
    depends_on:
      trustay-postgres:
        condition: service_healthy
    networks:
      - trustay-network

  trustay-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    restart: unless-stopped
    container_name: trustay-app
    ports:
      - "3000:3000"
    env_file:
      - .env
    environment:
      - NODE_ENV=${NODE_ENV}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - RUN_SEED=false  # Set to true if you want to run seeding
      - FORCE_MIGRATION_BASELINE=false  # Set to true to force baseline resolution
    depends_on:
      trustay-postgres:
        condition: service_healthy
      trustay-redis:
        condition: service_healthy
    networks:
      - trustay-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://0.0.0.0:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  trustay-network:
    driver: bridge