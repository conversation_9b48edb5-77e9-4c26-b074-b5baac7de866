// Truststay Database Schema - Prisma
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================
// ENUMS
// ========================================

enum Gender {
  male
  female
  other
}

enum UserRole {
  tenant
  landlord
}

enum RoomType {
  boarding_house // Nhà trọ
  dormitory // Ký túc xá
  sleepbox // Sleepbox
  apartment // Chung cư
  whole_house // Nhà nguyên căn
}

enum BookingStatus {
  pending
  approved
  rejected
  cancelled
}

enum RentalStatus {
  active
  terminated
  expired
  pending_renewal
}

enum InvitationStatus {
  pending
  accepted
  declined
  expired
}

enum BillStatus {
  draft
  pending
  paid
  overdue
  cancelled
}

enum PaymentType {
  rent
  deposit
  utility
  fee
  refund
}

enum PaymentMethod {
  bank_transfer
  cash
  e_wallet
  card
}

enum PaymentStatus {
  pending
  completed
  failed
  refunded
}

enum ReviewerType {
  tenant
  owner
}

enum AmenityCategory {
  basic
  kitchen
  bathroom
  entertainment
  safety
  connectivity
  building
}

enum CostCategory {
  utility
  service
  parking
  maintenance
}

enum RuleCategory {
  smoking // Hút thuốc
  pets // Thú cưng
  visitors // Khách thăm
  noise // Tiếng ồn
  cleanliness // Vệ sinh
  security // An ninh
  usage // Sử dụng
  other // Khác
}

enum RuleType {
  allowed // Được phép
  forbidden // Cấm
  required // Bắt buộc
  conditional // Có điều kiện
}

enum CostType {
  fixed // Giá cố định hàng tháng
  per_unit // Theo đơn vị (kWh, m³)
  metered // Theo đồng hồ
  percentage // Theo phần trăm
  tiered // Bậc thang (khác nhau theo mức sử dụng)
}

enum BillingCycle {
  daily
  weekly
  monthly
  quarterly
  yearly
  per_use
}

enum Visibility {
  anyoneCanFind
  anyoneWithLink
  domainCanFind
  domainWithLink
  limited
}

enum SearchPostStatus {
  active
  paused
  closed
  expired
}

enum VerificationType {
  email
  phone
  password_reset
}

enum VerificationStatus {
  pending
  verified
  expired
  failed
}

// ========================================
// USER MANAGEMENT
// ========================================

model User {
  id                 String    @id @default(uuid())
  email              String    @unique
  phone              String?   @unique
  passwordHash       String    @map("password_hash")
  firstName          String    @map("first_name")
  lastName           String    @map("last_name")
  avatarUrl          String?   @map("avatar_url")
  dateOfBirth        DateTime? @map("date_of_birth") @db.Date
  gender             Gender?
  role               UserRole  @default(tenant)
  bio                String?
  idCardNumber       String?   @map("id_card_number")
  idCardImages       String[]  @map("id_card_images")
  bankAccount        String?   @map("bank_account")
  bankName           String?   @map("bank_name")
  isVerifiedPhone    Boolean   @default(false) @map("is_verified_phone")
  isVerifiedEmail    Boolean   @default(false) @map("is_verified_email")
  isVerifiedIdentity Boolean   @default(false) @map("is_verified_identity")
  isVerifiedBank     Boolean   @default(false) @map("is_verified_bank")
  lastActiveAt       DateTime? @map("last_active_at") @db.Timestamp
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")

  // Relations
  addresses           UserAddress[]
  ownedBuildings      Building[]         @relation("BuildingOwner")
  bookingRequests     BookingRequest[]
  rentalsAsTenant     Rental[]           @relation("RentalTenant")
  rentalsAsOwner      Rental[]           @relation("RentalOwner")
  payments            Payment[]
  sentInvitations     RoomInvitation[]   @relation("InvitationSender")
  receivedInvitations RoomInvitation[]   @relation("InvitationReceiver")
  reviewsGiven        Review[]           @relation("ReviewGiver")
  reviewsReceived     Review[]           @relation("ReviewReceiver")
  notifications       Notification[]
  roomSearchPosts     RoomSearchPost[]
  verificationCodes   VerificationCode[]
  refreshTokens       RefreshToken[]

  @@map("users")
}

model VerificationCode {
  id          String             @id @default(uuid())
  userId      String?            @map("user_id") // Optional vì có thể verify trước khi tạo user
  email       String? // Email hoặc phone được verify
  phone       String?
  type        VerificationType
  code        String // 6-digit verification code
  status      VerificationStatus @default(pending)
  attempts    Int                @default(0) // Số lần thử verify
  maxAttempts Int                @default(5) // Tối đa 5 lần thử
  expiresAt   DateTime           @map("expires_at")
  verifiedAt  DateTime?          @map("verified_at")
  createdAt   DateTime           @default(now()) @map("created_at")
  updatedAt   DateTime           @updatedAt @map("updated_at")

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([email])
  @@index([phone])
  @@index([code])
  @@index([type])
  @@index([status])
  @@index([expiresAt])
  @@map("verification_codes")
}

model RefreshToken {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  token     String   @unique
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([token])
  @@index([expiresAt])
  @@map("refresh_tokens")
}

model UserAddress {
  id           String   @id @default(uuid())
  userId       String   @map("user_id")
  addressLine1 String   @map("address_line_1")
  addressLine2 String?  @map("address_line_2")
  wardId       Int?     @map("ward_id")
  districtId   Int      @map("district_id")
  provinceId   Int      @map("province_id")
  country      String   @default("Vietnam")
  postalCode   String?  @map("postal_code")
  isPrimary    Boolean  @default(false) @map("is_primary")
  createdAt    DateTime @default(now()) @map("created_at")

  // Relations
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  ward     Ward?    @relation(fields: [wardId], references: [id], onDelete: SetNull)
  district District @relation(fields: [districtId], references: [id], onDelete: Cascade)
  province Province @relation(fields: [provinceId], references: [id], onDelete: Cascade)

  @@index([districtId])
  @@index([provinceId])
  @@index([wardId])
  @@map("user_addresses")
}

// ========================================
// BUILDING & ROOM MANAGEMENT
// ========================================

model Building {
  id           String   @id // slug format: "nha-tro-minh-phat-quan-9"
  slug         String   @unique // auto-generated from name + district
  ownerId      String   @map("owner_id")
  name         String
  description  String?
  addressLine1 String   @map("address_line_1")
  addressLine2 String?  @map("address_line_2")
  wardId       Int?     @map("ward_id")
  districtId   Int      @map("district_id")
  provinceId   Int      @map("province_id")
  country      String   @default("Vietnam")
  latitude     Decimal? @db.Decimal(10, 7)
  longitude    Decimal? @db.Decimal(10, 7)
  isActive     Boolean  @default(true) @map("is_active")
  isVerified   Boolean  @default(false) @map("is_verified")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  owner    User     @relation("BuildingOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  ward     Ward?    @relation(fields: [wardId], references: [id], onDelete: SetNull)
  district District @relation(fields: [districtId], references: [id], onDelete: Cascade)
  province Province @relation(fields: [provinceId], references: [id], onDelete: Cascade)
  floors   Floor[]

  @@index([ownerId])
  @@index([districtId, provinceId])
  @@index([isActive])
  @@index([slug])
  @@index([latitude, longitude])
  @@index([districtId])
  @@index([provinceId])
  @@index([wardId])
  @@map("buildings")
}

model Floor {
  id          String   @id @default(uuid())
  buildingId  String   @map("building_id")
  floorNumber Int      @map("floor_number")
  name        String?
  description String?
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  building Building @relation(fields: [buildingId], references: [id], onDelete: Cascade)
  rooms    Room[]

  @@unique([buildingId, floorNumber])
  @@index([buildingId])
  @@map("floors")
}

model Room {
  id           String   @id // slug format: "nha-tro-minh-phat-phong-101"
  slug         String   @unique // auto-generated from building + room number
  floorId      String   @map("floor_id")
  roomNumber   String   @map("room_number")
  name         String?
  description  String?
  roomType     RoomType @map("room_type")
  areaSqm      Decimal? @map("area_sqm") @db.Decimal(8, 2)
  maxOccupancy Int      @default(1) @map("max_occupancy")
  isActive     Boolean  @default(true) @map("is_active")
  isVerified   Boolean  @default(false) @map("is_verified")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  floor           Floor            @relation(fields: [floorId], references: [id], onDelete: Cascade)
  amenities       RoomAmenity[]
  costs           RoomCost[]
  images          RoomImage[]
  rules           RoomRule[]
  pricing         RoomPricing?
  invitations     RoomInvitation[]
  bookingRequests BookingRequest[]
  rentals         Rental[]
  monthlyBills    MonthlyBill[]

  @@unique([floorId, roomNumber])
  @@index([floorId])
  @@index([roomType])
  @@index([isActive])
  @@index([slug])
  @@map("rooms")
}

model RoomImage {
  id        String   @id @default(uuid())
  roomId    String   @map("room_id")
  imageUrl  String   @map("image_url")
  altText   String?  @map("alt_text")
  sortOrder Int      @default(0) @map("sort_order")
  isPrimary Boolean  @default(false) @map("is_primary")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  room Room @relation(fields: [roomId], references: [id], onDelete: Cascade)

  @@map("room_images")
}

// ========================================
// SIMPLIFIED ROOM RULES SYSTEM
// ========================================

model SystemRoomRule {
  id          String       @id @default(uuid())
  name        String
  nameEn      String       @unique @map("name_en")
  category    RuleCategory
  ruleType    RuleType     @default(allowed)
  description String?
  isActive    Boolean      @default(true) @map("is_active")
  sortOrder   Int          @default(0) @map("sort_order")
  createdAt   DateTime     @default(now()) @map("created_at")
  updatedAt   DateTime     @updatedAt @map("updated_at")

  // Relations
  roomRules RoomRule[]

  @@index([category])
  @@index([isActive])
  @@map("system_room_rules")
}

model RoomRule {
  id           String   @id @default(uuid())
  roomId       String   @map("room_id")
  systemRuleId String   @map("system_rule_id")
  customValue  String?  @map("custom_value")
  isEnforced   Boolean  @default(true) @map("is_enforced")
  notes        String?
  createdAt    DateTime @default(now()) @map("created_at")

  // Relations
  room       Room           @relation(fields: [roomId], references: [id], onDelete: Cascade)
  systemRule SystemRoomRule @relation(fields: [systemRuleId], references: [id], onDelete: Cascade)

  @@unique([roomId, systemRuleId])
  @@index([roomId])
  @@index([systemRuleId])
  @@map("room_rules")
}

// ========================================
// SIMPLIFIED AMENITIES SYSTEM
// ========================================

model SystemAmenity {
  id          String          @id @default(uuid())
  name        String
  nameEn      String          @unique @map("name_en")
  category    AmenityCategory
  description String?
  isActive    Boolean         @default(true) @map("is_active")
  sortOrder   Int             @default(0) @map("sort_order")
  createdAt   DateTime        @default(now()) @map("created_at")
  updatedAt   DateTime        @updatedAt @map("updated_at")

  // Relations
  roomAmenities RoomAmenity[]

  @@index([category])
  @@index([isActive])
  @@map("system_amenities")
}

model RoomAmenity {
  id              String   @id @default(uuid())
  roomId          String   @map("room_id")
  systemAmenityId String   @map("system_amenity_id")
  customValue     String?  @map("custom_value")
  notes           String?
  createdAt       DateTime @default(now()) @map("created_at")

  // Relations
  room          Room          @relation(fields: [roomId], references: [id], onDelete: Cascade)
  systemAmenity SystemAmenity @relation(fields: [systemAmenityId], references: [id], onDelete: Cascade)

  @@unique([roomId, systemAmenityId])
  @@index([roomId])
  @@index([systemAmenityId])
  @@map("room_amenities")
}

// ========================================
// SIMPLIFIED COST TYPES SYSTEM
// ========================================

model SystemCostType {
  id          String       @id @default(uuid())
  name        String
  nameEn      String       @unique @map("name_en")
  category    CostCategory
  defaultUnit String?      @map("default_unit")
  description String?
  isActive    Boolean      @default(true) @map("is_active")
  sortOrder   Int          @default(0) @map("sort_order")
  createdAt   DateTime     @default(now()) @map("created_at")
  updatedAt   DateTime     @updatedAt @map("updated_at")

  // Relations
  roomCosts RoomCost[]

  @@index([category])
  @@index([isActive])
  @@map("system_cost_types")
}

model RoomCost {
  id               String @id @default(uuid())
  roomId           String @map("room_id")
  systemCostTypeId String @map("system_cost_type_id")

  // Flexible pricing fields
  costType    CostType @default(fixed) @map("cost_type")
  baseRate    Decimal? @map("base_rate") @db.Decimal(15, 2)
  unitPrice   Decimal? @map("unit_price") @db.Decimal(15, 2)
  fixedAmount Decimal? @map("fixed_amount") @db.Decimal(15, 2)

  // Additional pricing info
  currency      String   @default("VND")
  unit          String? // Override default unit from SystemCostType
  minimumCharge Decimal? @map("minimum_charge") @db.Decimal(15, 2)
  maximumCharge Decimal? @map("maximum_charge") @db.Decimal(15, 2)

  // Usage tracking (for metered costs)
  isMetered        Boolean  @default(false) @map("is_metered")
  meterReading     Decimal? @map("meter_reading") @db.Decimal(15, 2)
  lastMeterReading Decimal? @map("last_meter_reading") @db.Decimal(15, 2)

  // Billing configuration
  billingCycle   BillingCycle @default(monthly) @map("billing_cycle")
  includedInRent Boolean      @default(false) @map("included_in_rent")
  isOptional     Boolean      @default(false) @map("is_optional")

  notes     String?
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  room           Room           @relation(fields: [roomId], references: [id], onDelete: Cascade)
  systemCostType SystemCostType @relation(fields: [systemCostTypeId], references: [id], onDelete: Cascade)

  @@unique([roomId, systemCostTypeId])
  @@index([roomId])
  @@index([isActive])
  @@map("room_costs")
}

// ========================================
// PRICING & AVAILABILITY
// ========================================

model RoomPricing {
  id                   String   @id @default(uuid())
  roomId               String   @unique @map("room_id")
  basePriceMonthly     Decimal  @map("base_price_monthly") @db.Decimal(15, 2)
  currency             String   @default("VND")
  depositAmount        Decimal  @map("deposit_amount") @db.Decimal(15, 2)
  depositMonths        Int      @default(1) @map("deposit_months")
  utilityIncluded      Boolean  @default(false) @map("utility_included")
  utilityCostMonthly   Decimal? @map("utility_cost_monthly") @db.Decimal(15, 2)
  cleaningFee          Decimal? @map("cleaning_fee") @db.Decimal(15, 2)
  serviceFeePercentage Decimal? @map("service_fee_percentage") @db.Decimal(5, 2)
  minimumStayMonths    Int      @default(1) @map("minimum_stay_months")
  maximumStayMonths    Int?     @map("maximum_stay_months")
  priceNegotiable      Boolean  @default(false) @map("price_negotiable")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  // Relations
  room Room @relation(fields: [roomId], references: [id], onDelete: Cascade)

  @@map("room_pricing")
}

// ========================================
// INVITATION & BOOKING MANAGEMENT
// ========================================

model RoomInvitation {
  id             String           @id @default(uuid())
  roomId         String           @map("room_id")
  senderId       String           @map("sender_id") // Landlord
  recipientId    String?          @map("recipient_id") // Tenant (optional if not registered)
  recipientEmail String?          @map("recipient_email") // For users not yet registered
  monthlyRent    Decimal          @map("monthly_rent") @db.Decimal(15, 2)
  depositAmount  Decimal          @map("deposit_amount") @db.Decimal(15, 2)
  moveInDate     DateTime?        @map("move_in_date") @db.Date
  rentalMonths   Int?             @map("rental_months")
  status         InvitationStatus @default(pending)
  message        String?
  expiresAt      DateTime?        @map("expires_at")
  respondedAt    DateTime?        @map("responded_at")
  createdAt      DateTime         @default(now()) @map("created_at")
  updatedAt      DateTime         @updatedAt @map("updated_at")

  // Relations
  room      Room    @relation(fields: [roomId], references: [id], onDelete: Cascade)
  sender    User    @relation("InvitationSender", fields: [senderId], references: [id], onDelete: Cascade)
  recipient User?   @relation("InvitationReceiver", fields: [recipientId], references: [id], onDelete: Cascade)
  rental    Rental?

  @@index([roomId])
  @@index([senderId])
  @@index([recipientId])
  @@index([status])
  @@index([expiresAt])
  @@map("room_invitations")
}

// ========================================
// BOOKING & RENTAL MANAGEMENT
// ========================================

model BookingRequest {
  id             String        @id @default(uuid())
  roomId         String        @map("room_id")
  tenantId       String        @map("tenant_id")
  moveInDate     DateTime      @map("move_in_date") @db.Date
  moveOutDate    DateTime?     @map("move_out_date") @db.Date
  rentalMonths   Int?          @map("rental_months")
  monthlyRent    Decimal       @map("monthly_rent") @db.Decimal(15, 2)
  depositAmount  Decimal       @map("deposit_amount") @db.Decimal(15, 2)
  totalAmount    Decimal       @map("total_amount") @db.Decimal(15, 2)
  status         BookingStatus @default(pending)
  messageToOwner String?       @map("message_to_owner")
  ownerNotes     String?       @map("owner_notes")
  createdAt      DateTime      @default(now()) @map("created_at")
  updatedAt      DateTime      @updatedAt @map("updated_at")

  // Relations
  room   Room    @relation(fields: [roomId], references: [id], onDelete: Cascade)
  tenant User    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  rental Rental?

  @@index([roomId])
  @@index([tenantId])
  @@index([status])
  @@map("booking_requests")
}

model Rental {
  id                    String       @id @default(uuid())
  bookingRequestId      String?      @unique @map("booking_request_id")
  invitationId          String?      @unique @map("invitation_id")
  roomId                String       @map("room_id")
  tenantId              String       @map("tenant_id")
  ownerId               String       @map("owner_id")
  contractStartDate     DateTime     @map("contract_start_date") @db.Date
  contractEndDate       DateTime?    @map("contract_end_date") @db.Date
  monthlyRent           Decimal      @map("monthly_rent") @db.Decimal(15, 2)
  depositPaid           Decimal      @map("deposit_paid") @db.Decimal(15, 2)
  status                RentalStatus @default(active)
  contractDocumentUrl   String?      @map("contract_document_url")
  terminationNoticeDate DateTime?    @map("termination_notice_date") @db.Date
  terminationReason     String?      @map("termination_reason")
  createdAt             DateTime     @default(now()) @map("created_at")
  updatedAt             DateTime     @updatedAt @map("updated_at")

  // Relations
  bookingRequest BookingRequest? @relation(fields: [bookingRequestId], references: [id])
  invitation     RoomInvitation? @relation(fields: [invitationId], references: [id])
  room           Room            @relation(fields: [roomId], references: [id], onDelete: Cascade)
  tenant         User            @relation("RentalTenant", fields: [tenantId], references: [id], onDelete: Cascade)
  owner          User            @relation("RentalOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  payments       Payment[]
  reviews        Review[]
  monthlyBills   MonthlyBill[]

  @@index([roomId])
  @@index([tenantId])
  @@index([status])
  @@map("rentals")
}

// ========================================
// MONTHLY BILLING SYSTEM
// ========================================

model MonthlyBill {
  id            String   @id @default(uuid())
  rentalId      String   @map("rental_id")
  roomId        String   @map("room_id")
  billingPeriod String   @map("billing_period") // "2025-01" format
  billingMonth  Int      @map("billing_month") // 1-12
  billingYear   Int      @map("billing_year")
  periodStart   DateTime @map("period_start") @db.Date
  periodEnd     DateTime @map("period_end") @db.Date

  // Totals
  subtotal        Decimal @db.Decimal(15, 2)
  discountAmount  Decimal @default(0) @map("discount_amount") @db.Decimal(15, 2)
  taxAmount       Decimal @default(0) @map("tax_amount") @db.Decimal(15, 2)
  totalAmount     Decimal @map("total_amount") @db.Decimal(15, 2)
  paidAmount      Decimal @default(0) @map("paid_amount") @db.Decimal(15, 2)
  remainingAmount Decimal @map("remaining_amount") @db.Decimal(15, 2)

  // Status & Dates
  status   BillStatus @default(draft)
  dueDate  DateTime   @map("due_date") @db.Date
  paidDate DateTime?  @map("paid_date")
  notes    String?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  rental    Rental     @relation(fields: [rentalId], references: [id], onDelete: Cascade)
  room      Room       @relation(fields: [roomId], references: [id], onDelete: Cascade)
  billItems BillItem[]
  payments  Payment[]

  @@unique([rentalId, billingPeriod])
  @@index([rentalId])
  @@index([roomId])
  @@index([status])
  @@index([dueDate])
  @@index([billingYear, billingMonth])
  @@map("monthly_bills")
}

model BillItem {
  id            String   @id @default(uuid())
  monthlyBillId String   @map("monthly_bill_id")
  itemType      String   @map("item_type") // "rent", "utility", "service", "other"
  itemName      String   @map("item_name")
  description   String?
  quantity      Decimal? @db.Decimal(10, 2)
  unitPrice     Decimal? @map("unit_price") @db.Decimal(15, 2)
  amount        Decimal  @db.Decimal(15, 2)
  currency      String   @default("VND")
  notes         String?
  createdAt     DateTime @default(now()) @map("created_at")

  // Relations  
  monthlyBill MonthlyBill @relation(fields: [monthlyBillId], references: [id], onDelete: Cascade)

  @@index([monthlyBillId])
  @@index([itemType])
  @@map("bill_items")
}

model Payment {
  id                   String         @id @default(uuid())
  rentalId             String         @map("rental_id")
  monthlyBillId        String?        @map("monthly_bill_id")
  payerId              String         @map("payer_id")
  paymentType          PaymentType    @map("payment_type")
  amount               Decimal        @db.Decimal(15, 2)
  currency             String         @default("VND")
  paymentMethod        PaymentMethod? @map("payment_method")
  paymentStatus        PaymentStatus  @default(pending) @map("payment_status")
  paymentDate          DateTime?      @map("payment_date")
  dueDate              DateTime?      @map("due_date") @db.Date
  description          String?
  transactionReference String?        @map("transaction_reference")
  createdAt            DateTime       @default(now()) @map("created_at")
  updatedAt            DateTime       @updatedAt @map("updated_at")

  // Relations
  rental      Rental       @relation(fields: [rentalId], references: [id], onDelete: Cascade)
  monthlyBill MonthlyBill? @relation(fields: [monthlyBillId], references: [id], onDelete: SetNull)
  payer       User         @relation(fields: [payerId], references: [id], onDelete: Cascade)

  @@index([rentalId])
  @@index([monthlyBillId])
  @@index([payerId])
  @@index([paymentStatus])
  @@index([paymentDate])
  @@map("payments")
}

// ========================================
// REVIEWS
// ========================================

model Review {
  id                  String       @id @default(uuid())
  rentalId            String       @map("rental_id")
  reviewerId          String       @map("reviewer_id")
  revieweeId          String       @map("reviewee_id")
  reviewerType        ReviewerType @map("reviewer_type")
  propertyRating      Int?         @map("property_rating")
  communicationRating Int?         @map("communication_rating")
  cleanlinessRating   Int?         @map("cleanliness_rating")
  overallRating       Int?         @map("overall_rating")
  reviewText          String?      @map("review_text")
  isPublic            Boolean      @default(true) @map("is_public")
  responseText        String?      @map("response_text")
  responseDate        DateTime?    @map("response_date")
  createdAt           DateTime     @default(now()) @map("created_at")

  // Relations
  rental   Rental @relation(fields: [rentalId], references: [id], onDelete: Cascade)
  reviewer User   @relation("ReviewGiver", fields: [reviewerId], references: [id], onDelete: Cascade)
  reviewee User   @relation("ReviewReceiver", fields: [revieweeId], references: [id], onDelete: Cascade)

  @@index([rentalId])
  @@index([revieweeId])
  @@map("reviews")
}

// ========================================
// ROOM SEARCH POSTS
// ========================================

model RoomSearchPost {
  id          String @id @default(uuid())
  tenantId    String @map("tenant_id")
  title       String
  description String

  // Location preferences
  preferredDistricts String[] @map("preferred_districts")
  preferredWards     String[] @map("preferred_wards")
  preferredCity      String   @map("preferred_city")
  preferredLatitude  Decimal? @map("preferred_latitude") @db.Decimal(10, 7)
  preferredLongitude Decimal? @map("preferred_longitude") @db.Decimal(10, 7)
  searchRadiusKm     Decimal? @map("search_radius_km") @db.Decimal(8, 2)

  // Budget preferences
  minBudget Decimal? @map("min_budget") @db.Decimal(15, 2)
  maxBudget Decimal  @map("max_budget") @db.Decimal(15, 2)
  currency  String   @default("VND")

  // Room preferences
  preferredRoomTypes RoomType[] @map("preferred_room_types")
  maxOccupancy       Int?       @map("max_occupancy")
  minAreaSqm         Decimal?   @map("min_area_sqm") @db.Decimal(8, 2)

  // Move-in preferences
  moveInDate     DateTime? @map("move_in_date") @db.Date
  rentalDuration Int?      @map("rental_duration") // months

  // Required amenities (JSON array of amenity IDs)
  requiredAmenities String[] @map("required_amenities")

  // Contact info
  contactPhone String? @map("contact_phone")
  contactEmail String? @map("contact_email")

  // Post settings
  status    SearchPostStatus @default(active)
  isPublic  Boolean          @default(true) @map("is_public")
  autoRenew Boolean          @default(false) @map("auto_renew")
  expiresAt DateTime?        @map("expires_at")

  // Engagement
  viewCount    Int @default(0) @map("view_count")
  contactCount Int @default(0) @map("contact_count")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  tenant User @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([status])
  @@index([preferredCity])
  @@index([maxBudget])
  @@index([moveInDate])
  @@index([expiresAt])
  @@index([createdAt])
  @@index([preferredLatitude, preferredLongitude])
  @@map("room_search_posts")
}

// ========================================
// SYSTEM TABLES
// ========================================

model Notification {
  id               String    @id @default(uuid())
  userId           String    @map("user_id")
  notificationType String    @map("notification_type")
  title            String
  message          String
  data             Json?
  isRead           Boolean   @default(false) @map("is_read")
  readAt           DateTime? @map("read_at")
  expiresAt        DateTime? @map("expires_at")
  createdAt        DateTime  @default(now()) @map("created_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// ========================================
// LOCATION TABLES
// ========================================
model Province {
  id        Int      @id @default(autoincrement())
  code      String   @unique @map("province_code")
  name      String   @map("province_name")
  nameEn    String?  @map("province_name_en")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  districts     District[]
  userAddresses UserAddress[]
  buildings     Building[]

  @@index([code])
  @@map("provinces")
}

model District {
  id         Int      @id @default(autoincrement())
  code       String   @unique @map("district_code")
  name       String   @map("district_name")
  nameEn     String?  @map("district_name_en")
  provinceId Int      @map("province_id")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  province      Province      @relation(fields: [provinceId], references: [id], onDelete: Cascade)
  wards         Ward[]
  userAddresses UserAddress[]
  buildings     Building[]

  @@index([code])
  @@index([provinceId])
  @@map("districts")
}

model Ward {
  id         Int      @id @default(autoincrement())
  code       String   @unique @map("ward_code")
  name       String   @map("ward_name")
  nameEn     String?  @map("ward_name_en")
  level      String   @map("ward_level") // "Phường", "Xã", "Thị trấn"
  districtId Int      @map("district_id")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  district      District      @relation(fields: [districtId], references: [id], onDelete: Cascade)
  userAddresses UserAddress[]
  buildings     Building[]

  @@index([code])
  @@index([districtId])
  @@map("wards")
}
