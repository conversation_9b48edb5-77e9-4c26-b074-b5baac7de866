const defaultAmenities = [
	// Basic amenities - Tiện ích cơ bản
	{
		name: '<PERSON><PERSON><PERSON><PERSON> hòa',
		nameEn: 'air_conditioning',
		category: 'basic',
		description: '<PERSON><PERSON><PERSON> điều hòa không khí',
		sortOrder: 1,
	},
	{
		name: '<PERSON><PERSON><PERSON> lạnh',
		nameEn: 'air_cooler',
		category: 'basic',
		description: '<PERSON><PERSON><PERSON> làm mát không khí',
		sortOrder: 2,
	},
	{
		name: 'Quạt trần',
		nameEn: 'ceiling_fan',
		category: 'basic',
		description: 'Quạt trần',
		sortOrder: 3,
	},
	{
		name: 'Tủ lạnh',
		nameEn: 'refrigerator',
		category: 'basic',
		description: 'Tủ lạnh',
		sortOrder: 4,
	},
	{
		name: '<PERSON>i<PERSON>ờng',
		nameEn: 'bed',
		category: 'basic',
		description: 'Giường ngủ',
		sortOrder: 5,
	},
	{
		name: 'T<PERSON> quần áo',
		nameEn: 'wardrobe',
		category: 'basic',
		description: 'Tủ để quần áo',
		sortOrder: 6,
	},
	{
		name: '<PERSON><PERSON><PERSON> học',
		nameEn: 'desk',
		category: 'basic',
		description: '<PERSON>àn học/làm việc',
		sortOrder: 7,
	},
	{
		name: 'Cửa sổ',
		nameEn: 'window',
		category: 'basic',
		description: 'Cửa sổ tự nhiên',
		sortOrder: 8,
	},
	{
		name: 'Ban công',
		nameEn: 'balcony',
		category: 'basic',
		description: 'Ban công riêng',
		sortOrder: 9,
	},

	// Kitchen amenities - Nhà bếp
	{
		name: 'Bếp gas',
		nameEn: 'gas_stove',
		category: 'kitchen',
		description: 'Bếp gas để nấu ăn',
		sortOrder: 10,
	},
	{
		name: 'Bếp điện từ',
		nameEn: 'induction_stove',
		category: 'kitchen',
		description: 'Bếp điện từ',
		sortOrder: 11,
	},
	{
		name: 'Lò vi sóng',
		nameEn: 'microwave',
		category: 'kitchen',
		description: 'Lò vi sóng',
		sortOrder: 12,
	},
	{
		name: 'Tủ bếp',
		nameEn: 'kitchen_cabinet',
		category: 'kitchen',
		description: 'Tủ bếp',
		sortOrder: 13,
	},
	{
		name: 'Nồi cơm điện',
		nameEn: 'rice_cooker',
		category: 'kitchen',
		description: 'Nồi cơm điện',
		sortOrder: 14,
	},

	// Bathroom amenities - Phòng tắm
	{
		name: 'Vòi sen',
		nameEn: 'shower',
		category: 'bathroom',
		description: 'Vòi sen tắm',
		sortOrder: 15,
	},
	{
		name: 'Toilet riêng',
		nameEn: 'private_toilet',
		category: 'bathroom',
		description: 'Nhà vệ sinh riêng',
		sortOrder: 16,
	},
	{
		name: 'Bình nóng lạnh',
		nameEn: 'water_heater',
		category: 'bathroom',
		description: 'Máy nước nóng',
		sortOrder: 17,
	},

	// Safety amenities - An toàn
	{
		name: 'Khóa vân tay',
		nameEn: 'fingerprint_lock',
		category: 'safety',
		description: 'Khóa cửa vân tay',
		sortOrder: 18,
	},
	{
		name: 'Khóa thẻ từ',
		nameEn: 'card_lock',
		category: 'safety',
		description: 'Khóa cửa thẻ từ',
		sortOrder: 19,
	},
	{
		name: 'Camera an ninh',
		nameEn: 'security_camera',
		category: 'safety',
		description: 'Camera giám sát',
		sortOrder: 20,
	},
	{
		name: 'Bảo vệ 24/7',
		nameEn: 'security_guard',
		category: 'safety',
		description: 'Dịch vụ bảo vệ 24/7',
		sortOrder: 21,
	},

	// Connectivity amenities - Kết nối
	{
		name: 'WiFi',
		nameEn: 'wifi',
		category: 'connectivity',
		description: 'Internet không dây',
		sortOrder: 22,
	},
	{
		name: 'Internet cáp quang',
		nameEn: 'fiber_internet',
		category: 'connectivity',
		description: 'Internet cáp quang tốc độ cao',
		sortOrder: 23,
	},

	// Building amenities - Tòa nhà
	{
		name: 'Thang máy',
		nameEn: 'elevator',
		category: 'building',
		description: 'Thang máy',
		sortOrder: 24,
	},
	{
		name: 'Máy giặt chung',
		nameEn: 'shared_laundry',
		category: 'building',
		description: 'Máy giặt sử dụng chung',
		sortOrder: 25,
	},
	{
		name: 'Máy giặt riêng',
		nameEn: 'private_laundry',
		category: 'building',
		description: 'Máy giặt riêng trong phòng',
		sortOrder: 26,
	},
	{
		name: 'Chỗ để xe máy',
		nameEn: 'motorbike_parking',
		category: 'building',
		description: 'Chỗ đậu xe máy',
		sortOrder: 27,
	},
	{
		name: 'Chỗ để xe ô tô',
		nameEn: 'car_parking',
		category: 'building',
		description: 'Chỗ đậu xe ô tô',
		sortOrder: 28,
	},
	{
		name: 'Sân phơi',
		nameEn: 'drying_area',
		category: 'building',
		description: 'Khu vực phơi đồ',
		sortOrder: 29,
	},
	{
		name: 'Khu vực tiếp khách',
		nameEn: 'reception_area',
		category: 'building',
		description: 'Khu vực tiếp khách chung',
		sortOrder: 30,
	},

	// Entertainment amenities - Giải trí
	{
		name: 'TV',
		nameEn: 'television',
		category: 'entertainment',
		description: 'Tivi',
		sortOrder: 31,
	},
	{
		name: 'Truyền hình cáp',
		nameEn: 'cable_tv',
		category: 'entertainment',
		description: 'Truyền hình cáp',
		sortOrder: 32,
	},

	// Location amenities - Vị trí địa lý
	{
		name: 'Gần chợ',
		nameEn: 'near_market',
		category: 'building',
		description: 'Gần chợ, siêu thị',
		sortOrder: 33,
	},
	{
		name: 'Gần trường học',
		nameEn: 'near_school',
		category: 'building',
		description: 'Gần trường học, đại học',
		sortOrder: 34,
	},
	{
		name: 'Gần bệnh viện',
		nameEn: 'near_hospital',
		category: 'building',
		description: 'Gần bệnh viện, phòng khám',
		sortOrder: 35,
	},
	{
		name: 'Gần công viên',
		nameEn: 'near_park',
		category: 'building',
		description: 'Gần công viên, khu vui chơi',
		sortOrder: 36,
	},
	{
		name: 'Gần trạm xe buýt',
		nameEn: 'near_bus_station',
		category: 'building',
		description: 'Gần trạm xe buýt, xe khách',
		sortOrder: 37,
	},
	{
		name: 'Gần nhà thờ',
		nameEn: 'near_church',
		category: 'building',
		description: 'Gần nhà thờ, chùa, đền',
		sortOrder: 38,
	},
	{
		name: 'Gần ngân hàng',
		nameEn: 'near_bank',
		category: 'building',
		description: 'Gần ngân hàng, ATM',
		sortOrder: 39,
	},
	{
		name: 'Gần quán ăn',
		nameEn: 'near_restaurant',
		category: 'building',
		description: 'Gần khu ẩm thực, quán ăn',
		sortOrder: 40,
	},
	{
		name: 'Gần trung tâm thương mại',
		nameEn: 'near_mall',
		category: 'building',
		description: 'Gần trung tâm thương mại',
		sortOrder: 41,
	},
	{
		name: 'Gần khu công nghiệp',
		nameEn: 'near_industrial_zone',
		category: 'building',
		description: 'Gần khu công nghiệp, khu chế xuất',
		sortOrder: 42,
	},
	{
		name: 'Gần sân bay',
		nameEn: 'near_airport',
		category: 'building',
		description: 'Gần sân bay',
		sortOrder: 43,
	},
	{
		name: 'Gần ga tàu',
		nameEn: 'near_train_station',
		category: 'building',
		description: 'Gần ga tàu hỏa',
		sortOrder: 44,
	},
	{
		name: 'Gần metro',
		nameEn: 'near_metro',
		category: 'building',
		description: 'Gần trạm metro, tàu điện ngầm',
		sortOrder: 45,
	},
	{
		name: 'Gần bãi biển',
		nameEn: 'near_beach',
		category: 'building',
		description: 'Gần bãi biển',
		sortOrder: 46,
	},
	{
		name: 'Gần khu du lịch',
		nameEn: 'near_tourist_area',
		category: 'building',
		description: 'Gần khu du lịch, điểm tham quan',
		sortOrder: 47,
	},
];

module.exports = { defaultAmenities };
