{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strict": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": false, "noImplicitReturns": false, "noImplicitThis": false, "noImplicitOverride": false, "exactOptionalPropertyTypes": false, "noUncheckedIndexedAccess": false, "resolveJsonModule": true, "esModuleInterop": true, "paths": {"@/*": ["src/*"], "@config/*": ["src/config/*"], "@common/*": ["src/common/*"], "@modules/*": ["src/modules/*"]}}, "include": ["src/**/*", "test/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}